/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.file

import net.ccbluex.liquidbounce.file.configs.CustomConfig
import net.ccbluex.liquidbounce.utils.client.ClientUtils.LOGGER
import java.io.File

/**
 * Manager for custom user configurations
 */
object CustomConfigManager {
    
    private val configsDir = File(FileManager.dir, "configs")
    
    init {
        // Create configs directory if it doesn't exist
        if (!configsDir.exists()) {
            configsDir.mkdirs()
        }
    }
    
    /**
     * Save current configuration with given name
     */
    fun saveConfig(name: String): Boolean {
        return try {
            val configFile = File(configsDir, "$name.json")
            val customConfig = CustomConfig(configFile)
            customConfig.saveConfig()
            LOGGER.info("[CustomConfigManager] Saved config: $name")
            true
        } catch (e: Exception) {
            LOGGER.error("[CustomConfigManager] Failed to save config: $name", e)
            false
        }
    }
    
    /**
     * Load configuration with given name
     */
    fun loadConfig(name: String): Boolean {
        return try {
            val configFile = File(configsDir, "$name.json")
            if (!configFile.exists()) {
                LOGGER.warn("[CustomConfigManager] Config file not found: $name")
                return false
            }
            
            val customConfig = CustomConfig(configFile)
            customConfig.loadConfig()
            LOGGER.info("[CustomConfigManager] Loaded config: $name")
            true
        } catch (e: Exception) {
            LOGGER.error("[CustomConfigManager] Failed to load config: $name", e)
            false
        }
    }
    
    /**
     * Delete configuration with given name
     */
    fun deleteConfig(name: String): Boolean {
        return try {
            val configFile = File(configsDir, "$name.json")
            if (configFile.exists()) {
                configFile.delete()
                LOGGER.info("[CustomConfigManager] Deleted config: $name")
                true
            } else {
                LOGGER.warn("[CustomConfigManager] Config file not found: $name")
                false
            }
        } catch (e: Exception) {
            LOGGER.error("[CustomConfigManager] Failed to delete config: $name", e)
            false
        }
    }
    
    /**
     * List all available configurations
     */
    fun listConfigs(): List<String> {
        return try {
            configsDir.listFiles { _, name -> name.endsWith(".json") }
                ?.map { it.nameWithoutExtension }
                ?.sorted()
                ?: emptyList()
        } catch (e: Exception) {
            LOGGER.error("[CustomConfigManager] Failed to list configs", e)
            emptyList()
        }
    }
    
    /**
     * Check if configuration exists
     */
    fun configExists(name: String): Boolean {
        val configFile = File(configsDir, "$name.json")
        return configFile.exists()
    }
}

/**
 * Configuration file information
 */
data class ConfigInfo(
    val name: String,
    val file: File,
    val lastModified: Long,
    val size: Long
)
