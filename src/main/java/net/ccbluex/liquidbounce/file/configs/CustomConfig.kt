/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.file.configs

import com.google.gson.JsonObject
import net.ccbluex.liquidbounce.LiquidBounce.moduleManager
import net.ccbluex.liquidbounce.file.FileConfig
import net.ccbluex.liquidbounce.utils.io.PRETTY_GSON
import net.ccbluex.liquidbounce.utils.io.json
import net.ccbluex.liquidbounce.utils.io.readJson
import java.io.File
import java.io.IOException

/**
 * Custom configuration file for user-defined configs
 */
class CustomConfig(file: File) : FileConfig(file) {

    /**
     * Load config from file
     *
     * @throws IOException
     */
    @Throws(IOException::class)
    override fun loadConfig() {
        val json = file.readJson() as? JsonObject ?: return

        // Load module states
        val modulesJson = json.getAsJsonObject("modules")
        if (modulesJson != null) {
            for ((key, value) in modulesJson.entrySet()) {
                val module = moduleManager[key] ?: continue
                val jsonModule = value as JsonObject
                
                if (jsonModule.has("State")) {
                    module.state = jsonModule["State"].asBoolean
                }
                if (jsonModule.has("KeyBind")) {
                    module.keyBind = jsonModule["KeyBind"].asInt
                }
            }
        }

        // Load module values
        val valuesJson = json.getAsJsonObject("values")
        if (valuesJson != null) {
            for ((key, value) in valuesJson.entrySet()) {
                val module = moduleManager[key] ?: continue
                val jsonModule = value as JsonObject
                
                for (moduleValue in module.values) {
                    val element = jsonModule[moduleValue.name]
                    if (element != null) {
                        moduleValue.fromJson(element)
                    }
                }
            }
        }
    }

    /**
     * Save config to file
     *
     * @throws IOException
     */
    @Throws(IOException::class)
    override fun saveConfig() {
        val jsonObject = json {
            // Save modules (states and keybinds)
            "modules" to json {
                for (module in moduleManager) {
                    module.name to json {
                        "State" to module.state
                        "KeyBind" to module.keyBind
                    }
                }
            }
            
            // Save values
            "values" to json {
                for (module in moduleManager) {
                    if (module.values.isNotEmpty()) {
                        module.name to json {
                            for (value in module.values) {
                                value.name to value.toJson()
                            }
                        }
                    }
                }
            }
            
            // Save metadata
            "metadata" to json {
                "configName" to file.nameWithoutExtension
                "createdAt" to System.currentTimeMillis()
                "clientVersion" to net.ccbluex.liquidbounce.LiquidBounce.clientVersionText
            }
        }
        
        file.writeText(PRETTY_GSON.toJson(jsonObject))
    }
}
